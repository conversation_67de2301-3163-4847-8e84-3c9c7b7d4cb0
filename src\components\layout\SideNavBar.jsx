import React from 'react';
import { <PERSON><PERSON><PERSON>, HiUser, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from 'react-icons/hi';
import '../../styles/SideNavBar.css';

const SideNavBar = ({
  activeTab,
  onTabChange,
  onLogout,
  currentUser
}) => {
  const navigationItems = [
    {
      id: 'home',
      label: 'Home',
      icon: HiHome
    },
    {
      id: 'courses',
      label: 'Courses',
      icon: HiBookOpen
    },
    {
      id: 'profile',
      label: 'Profile',
      icon: HiUser
    }
    // Future navigation items can be added here:
    // {
    //   id: 'categories',
    //   label: 'Categories',
    //   icon: HiFolder
    // }
  ];

  return (
    <div className="side-navbar">
      {/* Logo Section */}
      <div className="navbar-logo">
        <img src="logo.png" alt="logo" />
      </div>

      {/* Navigation Items */}
      <nav className="navbar-nav">
        {navigationItems.map((item) => {
          const IconComponent = item.icon;
          return (
            <button
              key={item.id}
              onClick={() => onTabChange(item.id)}
              className={`nav-item ${activeTab === item.id ? 'active' : ''}`}
            >
              <IconComponent className="nav-icon" />
              <span className="nav-label body3">{item.label}</span>
            </button>
          );
        })}
      </nav>

      {/* Logout Section */}
      <div className="navbar-logout">
        <button onClick={onLogout} className="logout-btn">
          <HiLogout className="logout-icon" />
          <span className="logout-label body3">Logout</span>
        </button>
      </div>
    </div>
  );
};

export default SideNavBar;
