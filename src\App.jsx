import React, { useEffect, useState } from "react";
import { BrowserRouter as Router, Routes, Route, Navigate, useLocation, useNavigate } from "react-router-dom";
import "./styles/App.css";
import { LoginPage } from "./pages";
import Dashboard from "./pages/Dashboard";
import { ErrorBoundary, LoadingSpinner } from "./components/common";
import { useAuthStore } from "./stores";

function AppContent() {
  const { isAuthenticated, initializeAuth, isLoading, token } = useAuthStore();
  const [isInitialized, setIsInitialized] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();

  // Initialize auth on mount - only run once
  useEffect(() => {
    let mounted = true;
    
    const init = async () => {
      console.log('[App] Initializing auth...');
      try {
        await initializeAuth();
        if (mounted) {
          console.log('[App] Auth initialization completed');
          setIsInitialized(true);
        }
      } catch (error) {
        console.error('[App] Auth init error:', error);
        if (mounted) {
          setIsInitialized(true);
        }
      }
    };
    
    init();
    
    return () => {
      mounted = false;
    };
  }, []); // Empty dependency array - run only once

  // Debug logging for auth state changes
  useEffect(() => {
    if (isInitialized) {
      console.log('[App] Auth state changed:', {
        isAuthenticated,
        hasToken: !!token,
        isLoading,
        currentPath: location.pathname
      });
    }
  }, [isAuthenticated, token, isLoading, isInitialized, location.pathname]);

  // Handle redirect after login
  useEffect(() => {
    if (isInitialized && !isLoading && isAuthenticated) {
      const params = new URLSearchParams(location.search);
      const redirect = params.get('redirect');
      
      if (redirect && redirect !== location.pathname) {
        console.log(`[App] Redirecting to: ${redirect}`);
        navigate(redirect, { replace: true });
      }
    }
  }, [isAuthenticated, isInitialized, isLoading, location.search, location.pathname, navigate]);

  // Show loading state while initializing
  if (!isInitialized) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <LoadingSpinner size="lg" />
        <span className="ml-2">Loading application...</span>
      </div>
    );
  }

  console.log('[App] Rendering routes with auth state:', { isAuthenticated, isInitialized });

  return (
    <ErrorBoundary>
      <Routes>
        <Route
          path="/login"
          element={
            isAuthenticated ? (
              <Navigate to="/dashboard" replace />
            ) : (
              <LoginPage />
            )
          }
        />
        <Route
          path="/dashboard/*"
          element={
            isAuthenticated ? (
              <Dashboard />
            ) : (
              <Navigate 
                to={`/login?redirect=${encodeURIComponent(location.pathname)}`} 
                replace 
              />
            )
          }
        />
        <Route
          path="/"
          element={
            <Navigate 
              to={isAuthenticated ? "/dashboard" : "/login"} 
              replace 
            />
          }
        />
        <Route
          path="*"
          element={
            <div className="flex flex-col items-center justify-center min-h-screen">
              <h1 className="text-2xl font-bold mb-4">404 - Page Not Found</h1>
              <button
                onClick={() => navigate(isAuthenticated ? '/dashboard' : '/login')}
                className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700"
              >
                Go to {isAuthenticated ? 'Dashboard' : 'Login'}
              </button>
            </div>
          }
        />
      </Routes>
    </ErrorBoundary>
  );
}

function App() {
  return (
    <Router>
      <AppContent />
    </Router>
  );
}

export default App;