import { useState, useCallback } from 'react';

/**
 * Custom hook for handling errors throughout the application
 * Provides centralized error management with toast notifications
 */
export const useErrorHandler = () => {
  const [errors, setErrors] = useState([]);

  // Add a new error
  const addError = useCallback((error) => {
    const errorObj = {
      id: error.id || Date.now(),
      message: error.message || 'An error occurred',
      type: error.type || 'error',
      timestamp: new Date().toISOString()
    };

    setErrors(prev => [...prev, errorObj]);

    // Auto-remove error after 5 seconds
    setTimeout(() => {
      removeError(errorObj.id);
    }, 5000);

    return errorObj.id;
  }, []);

  // Remove a specific error
  const removeError = useCallback((errorId) => {
    setErrors(prev => prev.filter(error => error.id !== errorId));
  }, []);

  // Clear all errors
  const clearErrors = useCallback(() => {
    setErrors([]);
  }, []);

  // Handle different types of errors
  const handleError = useCallback((error, context = {}) => {
    console.error('Error handled:', error, context);

    let errorMessage = 'An error occurred';
    let errorType = 'error';

    // Extract error message from different error types
    if (typeof error === 'string') {
      errorMessage = error;
    } else if (error?.message) {
      errorMessage = error.message;
    } else if (error?.response?.data?.message) {
      errorMessage = error.response.data.message;
    }

    // Determine error type based on context or error properties
    if (error?.response?.status === 401) {
      errorType = 'authentication';
    } else if (error?.response?.status === 403) {
      errorType = 'authorization';
    } else if (error?.response?.status >= 500) {
      errorType = 'server';
    }

    return addError({
      message: errorMessage,
      type: errorType,
      context
    });
  }, [addError]);

  // Clear error (alias for removeError for backward compatibility)
  const clearError = useCallback((errorId) => {
    if (errorId) {
      removeError(errorId);
    } else {
      clearErrors();
    }
  }, [removeError, clearErrors]);

  return {
    errors,
    error: errors[0] || null, // Return the first error for backward compatibility
    addError,
    removeError,
    clearErrors,
    handleError,
    clearError,
    hasErrors: errors.length > 0
  };
};
