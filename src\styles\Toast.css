/* Toast Notification Styles */
.toast-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-width: 400px;
}

.toast {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-left: 4px solid;
  opacity: 0;
  transform: translateX(100%);
  transition: all 0.3s ease;
  max-width: 100%;
  word-wrap: break-word;
}

.toast--visible {
  opacity: 1;
  transform: translateX(0);
}

.toast--exiting {
  opacity: 0;
  transform: translateX(100%);
}

/* Toast Types */
.toast--success {
  border-left-color: #28a745;
}

.toast--error {
  border-left-color: #dc3545;
}

.toast--warning {
  border-left-color: #ffc107;
}

.toast--info {
  border-left-color: #17a2b8;
}

.toast__icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  font-weight: bold;
  font-size: 14px;
  flex-shrink: 0;
}

.toast--success .toast__icon {
  background-color: #28a745;
  color: white;
}

.toast--error .toast__icon {
  background-color: #dc3545;
  color: white;
}

.toast--warning .toast__icon {
  background-color: #ffc107;
  color: #212529;
}

.toast--info .toast__icon {
  background-color: #17a2b8;
  color: white;
}

.toast__content {
  flex: 1;
  min-width: 0;
}

.toast__message {
  margin: 0;
  color: var(--text-color);
  font-size: 14px;
  line-height: 1.4;
}

.toast__close {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: var(--secondary-text-color);
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.toast__close:hover {
  background-color: rgba(0, 0, 0, 0.1);
  color: var(--text-color);
}

/* Responsive Design */
@media (max-width: 768px) {
  .toast-container {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }
  
  .toast {
    padding: 12px;
  }
  
  .toast__message {
    font-size: 13px;
  }
}
