# Authentication System Documentation

## Overview

The Somayya Academy Admin Portal uses a secure authentication system with password encryption and JWT token management.

## Password Encryption

### How it works
- User enters their plain text password in the login form
- The password is encrypted using AES-GCM encryption with a deterministic IV
- The same password will always produce the same encrypted string
- The encrypted password is sent to the backend API

### Encryption Details
- **Algorithm**: AES-GCM with 128-bit key
- **Key**: Derived from "somayyaacademy" using SHA-256
- **IV**: Deterministic, generated from password + key hash (first 12 bytes)
- **Format**: `base64(iv):base64(ciphertext)`

### Code Example
```javascript
import { encryptPassword } from './src/utils/crypto.js';

const plainPassword = 'userPassword123';
const encryptedPassword = await encryptPassword(plainPassword);
// Same password always produces the same encrypted string
```

## API Integration

### Login Endpoint
- **URL**: `POST /auth/signin`
- **Base URL**: `http://*************:8096/somayya-academy/api/v1`

### Request Payload
```json
{
  "email": "<EMAIL>",
  "password": "HDUHilJQCFDVkX4i:EiweDB/HFAXlrY51Y2DSsWEpt4JRqYw4SDU=",
  "portalType": "ADMIN"
}
```

### Response
```json
{
  "message": "Login successful.",
  "statusCode": 200,
  "data": {
    "userEmailId": "<EMAIL>",
    "is_profile_set": false,
    "userId": "78ee1be2-da64-4ea2-9d4a-e315c3e0dfba",
    "token": "eyJhbGciOiJIUzI1NiJ9.*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.tLuRLZhtgBRl4XAN6lUp3pdp92HOnitjBoo9sSk8wGQ"
  }
}
```

## JWT Token Management

### Storage
- JWT token is extracted from `response.data.token`
- Token is stored in `localStorage` with key `auth-token`
- User data is persisted in Zustand store with key `auth-storage`

### User Data Extraction
- User ID: `response.data.userId`
- Email: `response.data.userEmailId`
- Profile Status: `response.data.is_profile_set`
- Authorities: Extracted from JWT token payload

### API Headers
- All authenticated API requests automatically include:
  ```
  Authorization: Bearer {jwt-token}
  ```

### Token Expiration
- 401 responses automatically clear the token and redirect to login
- Token verification on app initialization

## Authentication Flow

### Login Process
1. User enters email and password
2. Password is encrypted using `encryptPassword()`
3. API call to `/auth/signin` with encrypted password
4. JWT token received and stored
5. User redirected to dashboard

### Logout Process
1. API call to `/auth/signout` (optional)
2. Clear token from localStorage
3. Clear user data from store
4. Redirect to login page

### Auto-Authentication
1. On app load, check for stored token
2. Verify token with `/auth/verify` endpoint
3. Restore user session if valid
4. Clear invalid tokens

## Security Features

### Password Security
- Passwords are never stored in plain text
- Deterministic encryption ensures consistency
- Client-side encryption before transmission

### Token Security
- JWT tokens for stateless authentication
- Automatic token attachment to requests
- Token expiration handling

### Error Handling
- Network error detection
- API error message display
- Graceful fallback for failed requests

## Development Testing

### Test Encryption
```javascript
import { testPasswordEncryption } from './src/utils/testEncryption.js';

// Test that same password produces same encrypted string
const result = await testPasswordEncryption();
console.log(result);
```

### Environment Configuration
```env
VITE_API_BASE_URL=http://*************:8096/somayya-academy/api/v1
VITE_API_TIMEOUT=10000
```

## File Structure

```
src/
├── services/
│   └── api.js              # API client and auth endpoints
├── stores/
│   └── authStore.js        # Authentication state management
├── utils/
│   ├── crypto.js           # Password encryption utilities
│   └── testEncryption.js   # Development testing
├── pages/auth/
│   └── LoginPage.jsx       # Login form component
└── routes/
    ├── PrivateRoute.jsx    # Protected route wrapper
    └── PublicRoute.jsx     # Public route wrapper
```

## Usage Examples

### Login Component
```jsx
import { useAuthStore } from '../stores/authStore';

const { login, isLoading, error } = useAuthStore();

const handleLogin = async (email, password) => {
  const result = await login(email, password);
  if (result.success) {
    navigate('/dashboard');
  }
};
```

### Protected API Call
```javascript
import { api } from '../services/api';

// Token is automatically added to headers
const response = await api.get('/admin/users');
```

### Logout
```jsx
const { logout } = useAuthStore();

const handleLogout = async () => {
  await logout();
  navigate('/login');
};
```
