import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import { coursesAPI } from '../services/api';
import SideNavBar from '../components/layout/SideNavBar';
import DashboardMainContent from '../components/layout/DashboardMainContent';
import '../styles/Dashboard.css';

const Dashboard = () => {
  const navigate = useNavigate();
  const { currentUser, logout, isAuthenticated } = useAuthStore();
  const [activeTab, setActiveTab] = useState('home');
  const [courses, setCourses] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Redirect if not authenticated
  useEffect(() => {
    if (!isAuthenticated) {
      navigate('/login');
    }
  }, [isAuthenticated, navigate]);

  // Fetch courses when dashboard loads
  useEffect(() => {
    const fetchCourses = async () => {
      if (!isAuthenticated) return;

      try {
        setLoading(true);
        setError(null);
        console.log('Fetching courses...');

        // Fetch courses with pagination parameters
        const response = await coursesAPI.getAll({
          offset: 0,
          limit: 20 // Fetch more courses for the dashboard
        });

        console.log('Courses fetched successfully:', response);

        // Transform API response to match component expectations
        const transformedCourses = response.data.map(course => ({
          id: course.id,
          title: course.title,
          price: course.offerPrice ? `₹${course.offerPrice}` : (course.originalPrice ? `₹${course.originalPrice}` : '₹TBD'),
          originalPrice: course.originalPrice ? `₹${course.originalPrice}` : null,
          duration: "4 months", // Default duration as API doesn't provide this
          image: course.displayPicture,
          description: course.description,
          status: course.status,
          tags: course.tags
        }));

        setCourses(transformedCourses);

      } catch (error) {
        console.error('Failed to fetch courses:', error);
        setError(error.message || 'Failed to load courses');
        // Set empty array on error to prevent UI issues
        setCourses([]);
      } finally {
        setLoading(false);
      }
    };

    fetchCourses();
  }, [isAuthenticated]);

  // Handle tab change - navigate to different routes
  const handleTabChange = (tabId) => {
    if (tabId === 'home') {
      setActiveTab('home');
    } else if (tabId === 'profile') {
      navigate('/profile');
    }
    // Add more navigation logic for future tabs
  };

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const handleCourseClick = (courseId) => {
    // Admin portal - courses are for management, not reading
    console.log('Course selected for management:', courseId);
    // TODO: Navigate to course management page when implemented
  };



  return (
    <div className="dashboard-container">
      <SideNavBar
        activeTab={activeTab}
        onTabChange={handleTabChange}
        onLogout={handleLogout}
        currentUser={currentUser}
      />
      <DashboardMainContent
        activeTab={activeTab}
        courses={courses}
        loading={loading}
        error={error}
        onCourseClick={handleCourseClick}
      />


    </div>
  );
};

export default Dashboard;
