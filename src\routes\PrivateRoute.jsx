import { useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';

/**
 * PrivateRoute component - Only accessible when authenticated
 * Redirects to login if user is not authenticated
 */
const PrivateRoute = ({ children }) => {
  const location = useLocation();
  const { isAuthenticated, isLoading, token } = useAuthStore();
  
  useEffect(() => {
    console.log('[PrivateRoute] Auth state:', {
      isAuthenticated,
      hasToken: !!token,
      isLoading,
      path: location.pathname
    });
  }, [isAuthenticated, isLoading, token, location.pathname]);
  
  if (isLoading) {
    console.log('[PrivateRoute] Loading auth state...');
    return <div>Loading...</div>; // Or a proper loading component
  }
  
  if (!isAuthenticated) {
    console.log('[PrivateRoute] Not authenticated, redirecting to login', {
      from: location.pathname
    });
    // Redirect to login, but save the current location they were trying to go to
    return <Navigate to="/login" state={{ from: location }} replace />;
  }
  
  console.log('[PrivateRoute] Rendering protected route:', location.pathname);
  return children;
};

export default PrivateRoute;
