import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { authAPI } from '../services/api';
import { encryptPassword } from '../utils/crypto';

// Helper function to log auth state changes
const logAuthState = (action, state) => {
  console.log(`[AuthStore] ${action}:`, {
    isAuthenticated: state.isAuthenticated,
    token: state.token ? `${state.token.substring(0, 10)}...` : null,
    currentUser: state.currentUser?.email || null,
    isLoading: state.isLoading,
    error: state.error
  });
};

const useAuthStore = create(
  persist(
    (set, get) => ({
      // State
      currentUser: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      token: null,

      // Login action
      login: async (email, password) => {
        console.log('[AuthStore] Login attempt started for:', email);
        set({ isLoading: true, error: null });

        try {
          console.log('[AuthStore] Encrypting password...');
          const encryptedPassword = await encryptPassword(password);

          console.log('[AuthStore] Calling signIn API...');
          const response = await authAPI.signIn({
            email,
            password: encryptedPassword
          });
          
          console.log('[AuthStore] SignIn API response:', {
            status: response.statusCode,
            hasToken: !!response.data?.token,
            userData: response.data
          });

          const token = response.data?.token;
          const userData = response.data;
          
          if (!token) {
            throw new Error('No authentication token received');
          }

          console.log('[AuthStore] Storing token in localStorage');
          localStorage.setItem('auth-token', token);
          
          // Create user object from response
          const user = {
            id: userData.userId,
            email: userData.userEmailId,
            name: userData.userEmailId.split('@')[0],
            isProfileSet: userData.is_profile_set
          };

          const newState = {
            isAuthenticated: true,
            token,
            currentUser: user,
            isLoading: false,
            error: null
          };
          
          console.log('[AuthStore] Login successful, updating state');
          set(newState);
          logAuthState('Login successful', newState);
          
          return { success: true, user };
          
        } catch (error) {
          console.error('[AuthStore] Login error:', error);
          
          // Clear any stored token on error
          localStorage.removeItem('auth-token');
          
          const errorMessage = error.response?.data?.message || error.message || 'Login failed';
          
          const errorState = {
            isAuthenticated: false,
            token: null,
            currentUser: null,
            isLoading: false,
            error: errorMessage
          };

          set(errorState);
          logAuthState('Login failed', errorState);
          
          return { success: false, error: errorMessage };
        }
      },

      // Logout action
      logout: async () => {
        console.log('[AuthStore] Logout initiated');
        set({ isLoading: true });
        
        try {
          console.log('[AuthStore] Calling signOut API...');
          await authAPI.signOut();
          console.log('[AuthStore] SignOut API call successful');
        } catch (error) {
          console.warn('[AuthStore] Logout API call failed:', error.message);
        } finally {
          console.log('[AuthStore] Clearing auth state and storage');
          localStorage.removeItem('auth-token');
          
          const clearedState = {
            isAuthenticated: false,
            token: null,
            currentUser: null,
            error: null,
            isLoading: false
          };
          
          set(clearedState);
          logAuthState('Logout completed', clearedState);
        }
      },

      // Clear any error
      clearError: () => {
        set({ error: null });
        console.log('[AuthStore] Error cleared');
      },

      // Initialize auth state from localStorage
      initializeAuth: async () => {
        console.log('[AuthStore] Initializing auth...');
        
        try {
          const token = localStorage.getItem('auth-token');
          console.log('[AuthStore] Token in storage:', token ? 'exists' : 'not found');
          
          if (token) {
            console.log('[AuthStore] Token found, validating...');
            
            // Basic token validation (check if it's not expired)
            try {
              const payload = JSON.parse(atob(token.split('.')[1]));
              const now = Math.floor(Date.now() / 1000);
              
              if (payload.exp && payload.exp < now) {
                console.log('[AuthStore] Token expired, clearing storage');
                localStorage.removeItem('auth-token');
                throw new Error('Token expired');
              }
              
              console.log('[AuthStore] Token is valid, setting authenticated state');
              
              // Extract user info from token if available
              const userContext = payload.userContext || {};
              const user = {
                id: userContext.userId || payload.sub,
                email: userContext.userEmailId || userContext.username,
                name: userContext.userEmailId ? userContext.userEmailId.split('@')[0] : 'User'
              };
              
              const authenticatedState = {
                isAuthenticated: true,
                token,
                currentUser: user,
                isLoading: false,
                error: null
              };
              
              set(authenticatedState);
              logAuthState('Auth initialized - authenticated', authenticatedState);
              return;
              
            } catch (tokenError) {
              console.error('[AuthStore] Token validation failed:', tokenError);
              localStorage.removeItem('auth-token');
            }
          }
          
          // No valid token, set unauthenticated state
          console.log('[AuthStore] No valid token, setting unauthenticated state');
          const unauthenticatedState = {
            isAuthenticated: false,
            token: null,
            currentUser: null,
            isLoading: false,
            error: null
          };
          
          set(unauthenticatedState);
          logAuthState('Auth initialized - unauthenticated', unauthenticatedState);
          
        } catch (error) {
          console.error('[AuthStore] Auth initialization error:', error);
          
          // Clear everything on error
          localStorage.removeItem('auth-token');
          const errorState = {
            isAuthenticated: false,
            token: null,
            currentUser: null,
            isLoading: false,
            error: 'Authentication initialization failed'
          };
          
          set(errorState);
          logAuthState('Auth init error', errorState);
        }
      },

      // Force refresh auth state
      refreshAuth: () => {
        const { initializeAuth } = get();
        return initializeAuth();
      }
    }),
    {
      name: 'auth-storage',
      version: 1,
      // Only persist essential fields
      partialize: (state) => ({
        isAuthenticated: state.isAuthenticated,
        token: state.token,
        currentUser: state.currentUser
      }),
      // Storage event handlers
      onRehydrateStorage: () => (state, error) => {
        if (error) {
          console.error('[AuthStore] Storage rehydration error:', error);
        } else {
          console.log('[AuthStore] Storage rehydration completed:', {
            isAuthenticated: state?.isAuthenticated,
            hasToken: !!state?.token,
            hasUser: !!state?.currentUser
          });
        }
      },
      // Handle storage migration
      migrate: (persistedState, version) => {
        console.log('[AuthStore] Migrating storage from version:', version);
        if (version === 0) {
          // Clear old storage format
          return {
            isAuthenticated: false,
            token: null,
            currentUser: null
          };
        }
        return persistedState;
      },
      // Custom merge to ensure clean state
      merge: (persistedState, currentState) => {
        console.log('[AuthStore] Merging persisted state:', {
          persisted: {
            isAuthenticated: persistedState?.isAuthenticated,
            hasToken: !!persistedState?.token
          },
          current: {
            isAuthenticated: currentState?.isAuthenticated,
            hasToken: !!currentState?.token
          }
        });
        
        // If no persisted state, use current state
        if (!persistedState) {
          return currentState;
        }
        
        // Check if persisted token exists in localStorage
        const localToken = localStorage.getItem('auth-token');
        
        if (persistedState?.token) {
          if (!localToken) {
            console.warn('[AuthStore] Persisted token exists but localStorage is empty, using persisted token');
            // Restore token to localStorage
            localStorage.setItem('auth-token', persistedState.token);
            return { ...currentState, ...persistedState };
          } else if (persistedState.token !== localToken) {
            console.warn('[AuthStore] Token mismatch, using localStorage token');
            // Use localStorage token as source of truth
            return {
              ...currentState,
              ...persistedState,
              token: localToken
            };
          }
        } else if (localToken) {
          console.warn('[AuthStore] localStorage has token but persisted state doesn\'t, clearing localStorage');
          localStorage.removeItem('auth-token');
          return {
            ...currentState,
            isAuthenticated: false,
            token: null,
            currentUser: null
          };
        }
        
        return { ...currentState, ...persistedState };
      }
    }
  )
);

export { useAuthStore };