:root {
  /* Colors */
  --bg-color: #FFFBF3;
  --text-color: #111111;
  --secondary-text-color: #5F5F5F;
  --border-color: #C3BFB6;

  /* Font Family */
  --font-family-default: "Montserrat", -apple-system, BlinkMacSystemFont, "Segoe UI",
    Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;

  /* Font Weights */
  --font-weight-regular: 400;
  --font-weight-bold: 700;

  /* Headings */
  --h1-size: 48px;
  --h1-line-height: 52px;

  --h2-size: 40px;
  --h2-line-height: 44px;

  --h3-size: 32px;
  --h3-line-height: 40px;

  --h4-size: 24px;
  --h4-line-height: 32px;

  --h5-size: 20px;
  --h5-line-height: 24px;

  --h6-size: 16px;
  --h6-line-height: 20px;

  /* Body Regular */
  --body1-size: 20px;
  --body1-line-height: 28px;

  --body2-size: 16px;
  --body2-line-height: 24px;

  --body3-size: 14px;
  --body3-line-height: 20px;

  --body4-size: 12px;
  --body4-line-height: 16px;

  --body5-size: 10px;
  --body5-line-height: 12px;
}

/* === Typography Utility Classes === */

/* Headings */
.h1 {
  font-family: var(--font-family-default);
  font-size: var(--h1-size);
  line-height: var(--h1-line-height);
  font-weight: var(--font-weight-bold);
}

.h2 {
  font-family: var(--font-family-default);
  font-size: var(--h2-size);
  line-height: var(--h2-line-height);
  font-weight: var(--font-weight-bold);
}

.h3 {
  font-family: var(--font-family-default);
  font-size: var(--h3-size);
  line-height: var(--h3-line-height);
  font-weight: var(--font-weight-bold);
}

.h4 {
  font-family: var(--font-family-default);
  font-size: var(--h4-size);
  line-height: var(--h4-line-height);
  font-weight: var(--font-weight-bold);
}

.h5 {
  font-family: var(--font-family-default);
  font-size: var(--h5-size);
  line-height: var(--h5-line-height);
  font-weight: var(--font-weight-bold);
}

.h6 {
  font-family: var(--font-family-default);
  font-size: var(--h6-size);
  line-height: var(--h6-line-height);
  font-weight: var(--font-weight-bold);
}

/* Body Regular */
.body1 {
  font-family: var(--font-family-default);
  font-size: var(--body1-size);
  line-height: var(--body1-line-height);
  font-weight: var(--font-weight-regular);
}

.body2 {
  font-family: var(--font-family-default);
  font-size: var(--body2-size);
  line-height: var(--body2-line-height);
  font-weight: var(--font-weight-regular);
}

.body3 {
  font-family: var(--font-family-default);
  font-size: var(--body3-size);
  line-height: var(--body3-line-height);
  font-weight: var(--font-weight-regular);
}

.body4 {
  font-family: var(--font-family-default);
  font-size: var(--body4-size);
  line-height: var(--body4-line-height);
  font-weight: var(--font-weight-regular);
}

.body5 {
  font-family: var(--font-family-default);
  font-size: var(--body5-size);
  line-height: var(--body5-line-height);
  font-weight: var(--font-weight-regular);
}

/* Body Bold */
.body1-bold {
  font-family: var(--font-family-default);
  font-size: var(--body1-size);
  line-height: var(--body1-line-height);
  font-weight: var(--font-weight-bold);
}

.body2-bold {
  font-family: var(--font-family-default);
  font-size: var(--body2-size);
  line-height: var(--body2-line-height);
  font-weight: var(--font-weight-bold);
}

.body3-bold {
  font-family: var(--font-family-default);
  font-size: var(--body3-size);
  line-height: var(--body3-line-height);
  font-weight: var(--font-weight-bold);
}

.body4-bold {
  font-family: var(--font-family-default);
  font-size: var(--body4-size);
  line-height: var(--body4-line-height);
  font-weight: var(--font-weight-bold);
}

.body5-bold {
  font-family: var(--font-family-default);
  font-size: var(--body5-size);
  line-height: var(--body5-line-height);
  font-weight: var(--font-weight-bold);
}

html,
body {
  margin: 0;
  padding: 0;
  width: 100vw;
  max-width: 100vw;
  overflow-x: hidden;
  box-sizing: border-box;
  background-color: var(--bg-color);
  color: var(--text-color);
  font-family: var(--font-family-default);
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

body {
  font-family: "Montserrat", -apple-system, BlinkMacSystemFont, "Segoe UI",
    Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  background-color: #fffbf3;
  color: #333;
}

.app-container {
  display: flex;
  height: 100vh;
  height: 100dvh;
  /* Use dynamic viewport height for better mobile support */
  width: 100vw;
  max-width: 100vw;
  overflow: hidden;
  /* Prevent the entire page from scrolling */
}



/* === AnimationBox styles === */

.animation-box {
  position: relative;
  border-radius: 32px;
  border: 1px solid var(--border-color);
  background-color: #FFFBF34D;
  /* 30% white bg */
  backdrop-filter: blur(120px);
  -webkit-backdrop-filter: blur(120px);
  overflow: hidden;
  z-index: 1;
  padding: 0;
}

.animation-box-inner {
  padding: 2rem;
  position: relative;
  z-index: 2;
}

.animation-box-circle {
  position: absolute;
  width: 150px;
  height: 150px;
  background: linear-gradient(270deg, rgba(1, 6, 136, 0.5) -12%, rgba(157, 0, 2, 0.5) 130.33%);
  border-radius: 50%;
  filter: blur(80px);
  z-index: 0;
  animation: moveCirclePattern 20s ease-in-out infinite;
}

@keyframes moveCirclePattern {
  0% {
    top: -150px;
    left: 100%;
    transform: translate(-50%, 0);
  }

  16% {
    top: -150px;
    left: -150px;
    transform: translate(0, 0);
  }

  33% {
    top: 100%;
    left: 100%;
    transform: translate(-150px, -150px);
  }

  50% {
    top: 100%;
    left: -150px;
    transform: translate(0, -150px);
  }

  66% {
    top: 100%;
    left: 100%;
    transform: translate(-150px, -150px);
  }

  83% {
    top: -150px;
    left: -150px;
    transform: translate(0, 0);
  }

  100% {
    top: -150px;
    left: 100%;
    transform: translate(-50%, 0);
  }
}

/* === Header Component === */
.header-box {
  display: flex;
  width: 100%;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2rem;
  background-color: var(--bg-color);
  color: var(--text-color);
}

.header-logo img {
  height: 40px;
  object-fit: contain;
}

.header-buttons {
  display: flex;
  gap: 1rem;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.header-buttons button {
  background-color: transparent;
  color: var(--border-color);
  border: 1px solid var(--border-color);
  border-radius: 10px;
  padding: 0.4rem 0.7rem;
  cursor:pointer;
}

/*Privacy Page*/
.privacy-page {
  padding:2rem;
}

/*Terms Page*/
.terms-page{
  padding:2rem;
}

/* Responsive Design  */
@media (max-width: 1200px) {
  .course-structure-sidebar {
    flex-basis: 20%;
    max-width: 20%;
  }

  .tools-panel-sidebar {
    flex-basis: 20%;
    max-width: 20%;
  }
}

@media (max-width: 1024px) {
  .course-structure-sidebar {
    flex-basis: 20%;
    max-width: 20%;
  }

  .tools-panel-sidebar {
    flex-basis: 20%;
    max-width: 20%;
  }
}

@media (max-width: 768px) {
  .app-container {
    flex-direction: column;
    height: auto;
    min-height: 100vh;
  }

  .course-structure-sidebar,
  .main-content-panel,
  .tools-panel-sidebar {
    width: 100% !important;
    min-width: 100% !important;
    max-width: 100% !important;
    flex: none;
    height: auto;
  }

  .course-structure-sidebar {
    order: 1;
  }

  .main-content-panel {
    order: 2;
    min-height: 60vh;
  }

  .tools-panel-sidebar {
    order: 3;
  }
}