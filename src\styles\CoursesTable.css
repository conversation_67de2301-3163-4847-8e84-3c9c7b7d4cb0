/* Courses Page Layout */
.courses-page {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 4px 24px 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.courses-header {
  padding: 2px 0 12px;

  margin: 0;
}

.courses-header .header-content {
  max-width: 1200px;
  margin: 0 auto;
}

.courses-content {
  width: 100%;
}

.courses-table-container {
  background-color: var(--bg-color);
  border-radius: 12px;
  border: 1px solid var(--border-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  padding: 24px;
}

.courses-table-wrapper {
  overflow-x: auto;
  border-radius: 8px;
  background-color: var(--bg-color);
  margin-top: 16px;
}

.courses-table {
  width: 100%;
  border-collapse: collapse;
  font-family: var(--font-family-default);
}

.courses-table thead {
  background-color: #F8F6F0;
  border-bottom: 2px solid var(--border-color);
}

.courses-table th {
  padding: 16px 12px;
  text-align: left;
  font-weight: var(--font-weight-semibold);
  font-size: var(--body3-size);
  color: var(--text-color);
  border-right: 1px solid var(--border-color);
  white-space: nowrap;
}

.courses-table th:last-child {
  border-right: none;
}

.courses-table tbody tr {
  border-bottom: 1px solid var(--border-color);
  transition: background-color 0.2s ease;
}

.courses-table tbody tr:hover {
  background-color: #FDFCF8;
}

.courses-table tbody tr:last-child {
  border-bottom: none;
}

.courses-table td {
  padding: 16px 12px;
  font-size: var(--body3-size);
  color: var(--text-color);
  border-right: 1px solid var(--border-color);
  vertical-align: top;
}

.courses-table td:last-child {
  border-right: none;
}

/* Specific column styles */
.course-title {
  min-width: 200px;
  max-width: 300px;
}

.course-title-content {
  display: flex;
  flex-direction: column;
}

.course-title-text {
  font-weight: var(--font-weight-medium);
  color: var(--text-color);
  line-height: 1.4;
}

.course-description {
  min-width: 200px;
  max-width: 300px;
  line-height: 1.4;
}

.course-original-price,
.course-offer-price {
  min-width: 100px;
  font-weight: var(--font-weight-medium);
  text-align: right;
}

.course-status {
  min-width: 100px;
}

.status-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: var(--caption-size);
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-active {
  background-color: #E8F5E8;
  color: #2D5A2D;
}

.status-inactive {
  background-color: #FFF2E8;
  color: #8B4513;
}

.status-draft {
  background-color: #F0F0F0;
  color: #666666;
}

.status-unknown {
  background-color: #F5F5F5;
  color: var(--secondary-text-color);
}


.course-actions {
  min-width: 100px;
  text-align: center;
}

.action-button {
  padding: 6px 12px;
  border: 1px solid var(--border-color);
  border-radius: 4px;
  background-color: var(--bg-color);
  color: var(--text-color);
  font-size: var(--caption-size);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-button:hover {
  background-color: var(--text-color);
  color: var(--bg-color);
  transform: translateY(-1px);
}

.view-button {
  border-color: #4A90E2;
  color: #4A90E2;
}

.view-button:hover {
  background-color: #4A90E2;
  color: white;
}

.courses-table-footer {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
  text-align: center;
}

/* Loading, Error, and Empty States */
.loading-container,
.error-container,
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  text-align: center;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--border-color);
  border-top: 3px solid var(--text-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.retry-button {
  margin-top: 16px;
  padding: 8px 16px;
  background-color: var(--text-color);
  color: var(--bg-color);
  border: none;
  border-radius: 4px;
  font-size: var(--body3-size);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: opacity 0.2s ease;
}

.retry-button:hover {
  opacity: 0.8;
}

.text-error {
  color: #D32F2F;
}

/* Responsive Design */
@media (max-width: 1024px) {
  .courses-table-container {
    padding: 24px;
  }
  
  .courses-table th,
  .courses-table td {
    padding: 12px 8px;
  }
  
  .course-description {
    max-width: 200px;
  }
}

@media (max-width: 1024px) {
  .courses-page {
    padding: 0 16px 16px;
  }
  
  .courses-header {
    padding: 4px 0 8px;
  }
  
  .courses-table-container {
    padding: 16px;
  }
  
  .courses-table-wrapper {
    font-size: var(--caption-size);
  }
  
  .courses-table th,
  .courses-table td {
    padding: 8px 6px;
  }
  
  .course-title {
    min-width: 150px;
    max-width: 180px;
  }
  
  .course-description {
    min-width: 120px;
    max-width: 150px;
  }
}

@media (max-width: 640px) {
  .courses-table-wrapper {
    border-radius: 4px;
  }
  
  .courses-table th,
  .courses-table td {
    padding: 6px 4px;
    font-size: var(--caption-size);
  }
  
  .course-id {
    width: 60px;
    max-width: 60px;
  }
  
  .course-title {
    min-width: 120px;
    max-width: 140px;
  }
  
  .course-description {
    min-width: 100px;
    max-width: 120px;
  }
}
