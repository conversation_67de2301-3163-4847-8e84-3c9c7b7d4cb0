import { Navigate, useLocation } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';

/**
 * PublicRoute component - Only accessible when NOT authenticated
 * Redirects to dashboard if user is already authenticated
 */
const PublicRoute = ({ children }) => {
  const location = useLocation();
  const { isAuthenticated, isLoading } = useAuthStore();
  const from = location.state?.from?.pathname || '/dashboard';
  
  if (isLoading) {
    return <div>Loading...</div>; // Or a proper loading component
  }
  
  if (isAuthenticated) {
    // Redirect to the dashboard or the page they were trying to access before login
    return <Navigate to={from} replace />;
  }
  
  return children;
};

export default PublicRoute;
