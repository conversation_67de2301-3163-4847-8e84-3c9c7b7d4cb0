import { render, screen, fireEvent } from '@testing-library/react';
import { vi } from 'vitest';
import CoursesTable from '../CoursesTable';

describe('CoursesTable Component', () => {
  const mockCourses = [
    {
      id: '1',
      title: 'Test Course 1',
      description: 'This is a test course description',
      originalPrice: '1000',
      price: '₹800',
      status: 'active',
      tags: ['engineering', 'design']
    },
    {
      id: '2',
      title: 'Test Course 2',
      description: 'Another test course with a longer description that should be truncated when displayed in the table',
      originalPrice: '2000',
      price: '₹1500',
      status: 'inactive',
      tags: ['programming', 'web development']
    }
  ];

  const mockProps = {
    courses: mockCourses,
    loading: false,
    error: null,
    onCourseClick: vi.fn()
  };

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('should render courses table with data', () => {
    render(<CoursesTable {...mockProps} />);

    expect(screen.getByText('Courses')).toBeInTheDocument();
    expect(screen.getByText('Test Course 1')).toBeInTheDocument();
    expect(screen.getByText('Test Course 2')).toBeInTheDocument();
    expect(screen.getByText('This is a test course description')).toBeInTheDocument();
  });

  it('should render table headers correctly', () => {
    render(<CoursesTable {...mockProps} />);

    expect(screen.getByText('ID')).toBeInTheDocument();
    expect(screen.getByText('Title')).toBeInTheDocument();
    expect(screen.getByText('Description')).toBeInTheDocument();
    expect(screen.getByText('Original Price')).toBeInTheDocument();
    expect(screen.getByText('Offer Price')).toBeInTheDocument();
    expect(screen.getByText('Status')).toBeInTheDocument();
    expect(screen.getByText('Tags')).toBeInTheDocument();
    expect(screen.getByText('Actions')).toBeInTheDocument();
  });

  it('should display loading state', () => {
    render(<CoursesTable {...mockProps} loading={true} />);

    expect(screen.getByText('Loading courses...')).toBeInTheDocument();
    expect(screen.getByText('Courses')).toBeInTheDocument();
  });

  it('should display error state', () => {
    render(<CoursesTable {...mockProps} error="Failed to load data" />);

    expect(screen.getByText('Failed to load courses: Failed to load data')).toBeInTheDocument();
    expect(screen.getByText('Retry')).toBeInTheDocument();
  });

  it('should display empty state', () => {
    render(<CoursesTable {...mockProps} courses={[]} />);

    expect(screen.getByText('No courses available at the moment.')).toBeInTheDocument();
  });

  it('should call onCourseClick when view button is clicked', () => {
    render(<CoursesTable {...mockProps} />);

    const viewButtons = screen.getAllByText('View');
    fireEvent.click(viewButtons[0]);

    expect(mockProps.onCourseClick).toHaveBeenCalledWith('1');
  });

  it('should format status badges correctly', () => {
    render(<CoursesTable {...mockProps} />);

    expect(screen.getByText('Active')).toBeInTheDocument();
    expect(screen.getByText('Inactive')).toBeInTheDocument();
  });

  it('should display course count in footer', () => {
    render(<CoursesTable {...mockProps} />);

    expect(screen.getByText('Showing 2 courses')).toBeInTheDocument();
  });

  it('should display singular course count for single course', () => {
    render(<CoursesTable {...mockProps} courses={[mockCourses[0]]} />);

    expect(screen.getByText('Showing 1 course')).toBeInTheDocument();
  });

  it('should truncate long descriptions', () => {
    render(<CoursesTable {...mockProps} />);

    const longDescription = screen.getByText(/Another test course with a longer description/);
    expect(longDescription.textContent).toContain('...');
  });

  it('should format tags correctly', () => {
    render(<CoursesTable {...mockProps} />);

    expect(screen.getByText('engineering, design')).toBeInTheDocument();
    expect(screen.getByText('programming, web development')).toBeInTheDocument();
  });

  it('should handle courses with missing data gracefully', () => {
    const coursesWithMissingData = [
      {
        id: '3',
        title: 'Incomplete Course',
        description: null,
        originalPrice: null,
        price: null,
        status: null,
        tags: null
      }
    ];

    render(<CoursesTable {...mockProps} courses={coursesWithMissingData} />);

    expect(screen.getByText('Incomplete Course')).toBeInTheDocument();
    expect(screen.getByText('N/A')).toBeInTheDocument();
    expect(screen.getByText('Unknown')).toBeInTheDocument();
    expect(screen.getByText('No tags')).toBeInTheDocument();
  });

  it('should handle retry button click', () => {
    // Mock window.location.reload
    const mockReload = vi.fn();
    Object.defineProperty(window, 'location', {
      value: { reload: mockReload },
      writable: true,
    });

    render(<CoursesTable {...mockProps} error="Network error" />);

    const retryButton = screen.getByText('Retry');
    fireEvent.click(retryButton);

    expect(mockReload).toHaveBeenCalledTimes(1);
  });
});
