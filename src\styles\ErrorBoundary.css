/* Error Boundary Styles */
.error-boundary {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
  background-color: var(--bg-color);
}

.error-boundary-content {
  max-width: 500px;
  text-align: center;
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.error-icon {
  font-size: 48px;
  margin-bottom: 20px;
}

.error-boundary-content h2 {
  color: var(--text-color);
  margin-bottom: 16px;
  font-size: 24px;
}

.error-boundary-content p {
  color: var(--secondary-text-color);
  margin-bottom: 24px;
  line-height: 1.5;
}

.error-details {
  margin: 20px 0;
  text-align: left;
}

.error-details summary {
  cursor: pointer;
  padding: 8px;
  background-color: #f8f9fa;
  border-radius: 4px;
  margin-bottom: 8px;
}

.error-stack {
  background-color: #f8f9fa;
  padding: 12px;
  border-radius: 4px;
  font-family: monospace;
  font-size: 12px;
  overflow-x: auto;
  white-space: pre-wrap;
  color: #666;
}

.error-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  flex-wrap: wrap;
}

.retry-button,
.reload-button {
  padding: 12px 24px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.retry-button {
  background-color: var(--primary-color, #007bff);
  color: white;
}

.retry-button:hover {
  background-color: var(--primary-color-dark, #0056b3);
}

.reload-button {
  background-color: #6c757d;
  color: white;
}

.reload-button:hover {
  background-color: #545b62;
}
