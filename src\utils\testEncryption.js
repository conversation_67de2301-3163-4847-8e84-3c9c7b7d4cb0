/**
 * Test file to verify password encryption is working correctly
 * This file is for development testing only
 */

import { encryptPassword, decryptPassword } from './crypto.js';

/**
 * Test password encryption to ensure it's deterministic
 * Same password should always produce the same encrypted string
 */
export async function testPasswordEncryption() {
  console.log('🔐 Testing Password Encryption...');
  
  const testPassword = 'myTestPassword123';
  
  try {
    // Encrypt the same password multiple times
    const encrypted1 = await encryptPassword(testPassword);
    const encrypted2 = await encryptPassword(testPassword);
    const encrypted3 = await encryptPassword(testPassword);
    
    console.log('Password:', testPassword);
    console.log('Encrypted 1:', encrypted1);
    console.log('Encrypted 2:', encrypted2);
    console.log('Encrypted 3:', encrypted3);
    
    // Check if all encryptions are the same (deterministic)
    const isDeterministic = encrypted1 === encrypted2 && encrypted2 === encrypted3;
    console.log('✅ Is Deterministic:', isDeterministic);
    
    // Test decryption
    const decrypted = await decryptPassword(encrypted1);
    const isDecryptionCorrect = decrypted === testPassword;
    console.log('✅ Decryption Correct:', isDecryptionCorrect);
    
    return {
      isDeterministic,
      isDecryptionCorrect,
      encrypted: encrypted1,
      original: testPassword,
      decrypted
    };
  } catch (error) {
    console.error('❌ Encryption test failed:', error);
    return {
      error: error.message,
      isDeterministic: false,
      isDecryptionCorrect: false
    };
  }
}

/**
 * Test with different passwords to ensure they produce different encrypted strings
 */
export async function testDifferentPasswords() {
  console.log('🔐 Testing Different Passwords...');
  
  const passwords = ['password1', 'password2', 'differentPassword', '123456'];
  const encrypted = [];
  
  try {
    for (const password of passwords) {
      const encryptedPassword = await encryptPassword(password);
      encrypted.push({ original: password, encrypted: encryptedPassword });
      console.log(`"${password}" -> "${encryptedPassword}"`);
    }
    
    // Check that all encrypted passwords are different
    const encryptedStrings = encrypted.map(e => e.encrypted);
    const uniqueEncrypted = [...new Set(encryptedStrings)];
    const allDifferent = uniqueEncrypted.length === encryptedStrings.length;
    
    console.log('✅ All passwords produce different encrypted strings:', allDifferent);
    
    return {
      allDifferent,
      results: encrypted
    };
  } catch (error) {
    console.error('❌ Different passwords test failed:', error);
    return {
      error: error.message,
      allDifferent: false
    };
  }
}

// Run tests if this file is imported in development
if (process.env.NODE_ENV === 'development') {
  // You can uncomment these lines to run tests in the browser console
  // testPasswordEncryption().then(result => console.log('Encryption Test Result:', result));
  // testDifferentPasswords().then(result => console.log('Different Passwords Test Result:', result));
}
